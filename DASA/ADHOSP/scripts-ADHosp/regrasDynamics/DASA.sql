-- ========================================
-- Usuario do AD
-- ========================================

(
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
    OR
    (
        EXISTS
        (
            SELECT 1
            FROM (
                SELECT UID_Person
                FROM ADSAccount
                WHERE 1 = 1
            ) AS X
            WHERE X.UID_Person = Person.UID_Person
        )
    )
)
AND (isexternal = 0)
AND (IdentityType = N'Primary')
AND ISNULL(companymember, 'xx') NOT IN (
    'HOSPITAL SÃO LUCAS',
    'MATERNIDADE BRASÍLIA',
    'HOSPITAL BRASÍLIA',
    'HOSPITAL ÁGUAS CLARA',
    'HOSPITAL SANTA PAULA',
    'H9J',
    'CHN',
    'SANTA CELINA GI',
    'SAÚDE CELINA',
    'INNOVA'
)

-- ========================================
-- Usuario do AD com conta T
-- ========================================

(
    -- Verifica se a data de entrada é válida (não nula e menor/igual à data atual)
    -- OU se existe uma conta AD associada à pessoa
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
    OR
    (
        -- Verifica se existe pelo menos uma conta AD para esta pessoa
        EXISTS
        (
            SELECT 1
            FROM (
                -- Busca todas as pessoas que possuem conta no Active Directory
                SELECT UID_Person
                FROM ADSAccount
                WHERE 1 = 1  -- Condição sempre verdadeira (busca todos os registros)
            ) AS X
            WHERE X.UID_Person = Person.UID_Person  -- Relaciona com a pessoa atual
        )
    )
)
AND (isexternal = 1)  -- Filtra apenas usuários externos (não funcionários internos)
AND (IdentityType = N'Primary')  -- Considera apenas identidades primárias (não secundárias)
AND (isinactive = '0')  -- Exclui usuários inativos (apenas usuários ativos)
AND NOT (UID_FirmPartner = 'xxxxxxxxxxxxxxx')  -- Exclui parceiro específico da empresa

-- ========================================
-- Usuario do AD com conta T
-- ========================================

(
    -- Verifica se a data de entrada é válida (não nula e menor/igual à data atual)
    -- OU se existe uma conta AD associada à pessoa
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
    OR
    (
        -- Verifica se existe pelo menos uma conta AD para esta pessoa
        EXISTS
        (
            SELECT 1
            FROM (
                -- Busca todas as pessoas que possuem conta no Active Directory
                SELECT UID_Person
                FROM ADSAccount
                WHERE 1 = 1  -- Condição sempre verdadeira (busca todos os registros)
            ) AS X
            WHERE X.UID_Person = Person.UID_Person  -- Relaciona com a pessoa atual
        )
    )
)
AND (isexternal = 1)  -- Filtra apenas usuários externos (não funcionários internos)
AND (IdentityType = N'Primary')  -- Considera apenas identidades primárias (não secundárias)
AND (isinactive = '0')  -- Exclui usuários inativos (apenas usuários ativos)
AND NOT (UID_FirmPartner = 'xxxxxxxxxxxxxxx')  -- Exclui parceiro específico da empresa

-- ========================================
-- Usuario AD Hospitais
-- ========================================

(
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
    OR
    (
        EXISTS
        (
            SELECT 1
            FROM (
                SELECT UID_Person
                FROM ADSAccount
                WHERE 1 = 1
            ) AS X
            WHERE X.UID_Person = Person.UID_Person
        )
    )
)
AND (isexternal = 0)
AND (IdentityType = N'Primary')
AND ISNULL(companymember, 'xx') IN (
    'HOSPITAL SÃO LUCAS',
    'MATERNIDADE BRASÍLIA',
    'HOSPITAL BRASÍLIA',
    'HOSPITAL ÁGUAS CLARA',
    'HOSPITAL SANTA PAULA',
    'H9J',
    'IMPAR',
    'CHN',
    'INNOVA'
)

-- ========================================
-- Usuario AD CIIA
-- ========================================

(
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
    OR
    (
        EXISTS
        (
            SELECT 1
            FROM (
                SELECT UID_Person
                FROM ADSAccount
                WHERE 1 = 1
            ) AS X
            WHERE X.UID_Person = Person.UID_Person
        )
    )
)
AND (isexternal = 0)
AND (IdentityType = N'Primary')
AND ISNULL(companymember, 'xx') IN (
    'SANTA CELINA GI',
    'SAÚDE CELINA'
)

